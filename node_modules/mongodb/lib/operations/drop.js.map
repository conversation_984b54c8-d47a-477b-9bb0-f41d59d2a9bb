{"version": 3, "file": "drop.js", "sourceRoot": "", "sources": ["../../src/operations/drop.ts"], "names": [], "mappings": ";;;AAEA,oCAAiE;AAIjE,uCAAmF;AACnF,2CAAoD;AAQpD,gBAAgB;AAChB,MAAa,uBAAwB,SAAQ,kCAAiC;IAK5E,YAAY,EAAM,EAAE,IAAY,EAAE,UAAiC,EAAE;QACnE,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACnB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAEQ,eAAe,CACtB,MAAc,EACd,OAAkC,EAClC,QAA2B;QAE3B,CAAC,KAAK,IAAI,EAAE;YACV,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;YACnB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YAEvB,MAAM,kBAAkB,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,kBAAkB,CAAC;YAChF,IAAI,eAAe,GACjB,OAAO,CAAC,eAAe,IAAI,kBAAkB,EAAE,CAAC,GAAG,EAAE,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC,CAAC;YAEhF,IAAI,CAAC,eAAe,IAAI,kBAAkB,EAAE;gBAC1C,gEAAgE;gBAChE,kEAAkE;gBAClE,gEAAgE;gBAChE,qBAAqB;gBACrB,MAAM,qBAAqB,GAAG,MAAM,EAAE;qBACnC,eAAe,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;qBAC9C,OAAO,EAAE,CAAC;gBACb,eAAe,GAAG,qBAAqB,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,eAAe,CAAC;aACxE;YAED,IAAI,eAAe,EAAE;gBACnB,MAAM,aAAa,GAAG,eAAe,CAAC,aAAa,IAAI,WAAW,IAAI,MAAM,CAAC;gBAC7E,MAAM,cAAc,GAAG,eAAe,CAAC,cAAc,IAAI,WAAW,IAAI,OAAO,CAAC;gBAEhF,KAAK,MAAM,cAAc,IAAI,CAAC,aAAa,EAAE,cAAc,CAAC,EAAE;oBAC5D,4EAA4E;oBAC5E,MAAM,MAAM,GAAG,IAAI,uBAAuB,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;oBAC/D,IAAI;wBACF,MAAM,MAAM,CAAC,kCAAkC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;qBAClE;oBAAC,OAAO,GAAG,EAAE;wBACZ,IACE,CAAC,CAAC,GAAG,YAAY,wBAAgB,CAAC;4BAClC,GAAG,CAAC,IAAI,KAAK,2BAAmB,CAAC,iBAAiB,EAClD;4BACA,MAAM,GAAG,CAAC;yBACX;qBACF;iBACF;aACF;YAED,OAAO,IAAI,CAAC,kCAAkC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAClE,CAAC,CAAC,EAAE,CAAC,IAAI,CACP,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,EACrC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CACrB,CAAC;IACJ,CAAC;IAEO,kCAAkC,CACxC,MAAc,EACd,OAAkC;QAElC,OAAO,IAAI,OAAO,CAAU,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC9C,KAAK,CAAC,sBAAsB,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACjF,IAAI,GAAG;oBAAE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC5B,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA3ED,0DA2EC;AAKD,gBAAgB;AAChB,MAAa,qBAAsB,SAAQ,kCAAiC;IAG1E,YAAY,EAAM,EAAE,OAA4B;QAC9C,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IACQ,eAAe,CACtB,MAAc,EACd,OAAkC,EAClC,QAA2B;QAE3B,KAAK,CAAC,sBAAsB,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACjF,IAAI,GAAG;gBAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC9B,IAAI,MAAM,CAAC,EAAE;gBAAE,OAAO,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAChD,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAlBD,sDAkBC;AAED,IAAA,yBAAa,EAAC,uBAAuB,EAAE,CAAC,kBAAM,CAAC,eAAe,CAAC,CAAC,CAAC;AACjE,IAAA,yBAAa,EAAC,qBAAqB,EAAE,CAAC,kBAAM,CAAC,eAAe,CAAC,CAAC,CAAC"}