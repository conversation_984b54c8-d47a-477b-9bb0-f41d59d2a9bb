# Environment Configuration for Zero to Hero Developer Website

# Server Configuration
PORT=3000
NODE_ENV=development

# Session Secret (change this in production)
SESSION_SECRET=zero-to-hero-secret-key-change-in-production

# Database Configuration (for future use)
# MONGODB_URI=mongodb://localhost:27017/zero-to-hero
# DATABASE_URL=your-database-connection-string

# JWT Configuration (for future use)
# JWT_SECRET=your-jwt-secret-key
# JWT_EXPIRES_IN=24h

# Email Configuration (for future use)
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_USER=<EMAIL>
# EMAIL_PASS=your-email-password

# External API Keys (for future use)
# GITHUB_CLIENT_ID=your-github-client-id
# GITHUB_CLIENT_SECRET=your-github-client-secret
# GOOGLE_CLIENT_ID=your-google-client-id
# GOOGLE_CLIENT_SECRET=your-google-client-secret

# File Upload Configuration
# MAX_FILE_SIZE=5MB
# UPLOAD_PATH=./uploads

# Security Configuration
# CORS_ORIGIN=http://localhost:3000
# RATE_LIMIT_WINDOW=15
# RATE_LIMIT_MAX=100

# Logging Configuration
# LOG_LEVEL=info
# LOG_FILE=./logs/app.log
