const express = require('express');
const session = require('express-session');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static('public'));

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'zero-to-hero-secret-key',
  resave: false,
  saveUninitialized: false,
  cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 } // 24 hours
}));

// In-memory storage for demo (replace with database in production)
const users = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123', // In production, this should be hashed
    role: 'admin',
    progress: {},
    courseAccess: 'approved',
    approvedBy: 'system',
    approvedAt: new Date().toISOString()
  },
  {
    id: 2,
    username: 'user1',
    email: '<EMAIL>',
    password: 'user123', // In production, this should be hashed
    role: 'user',
    progress: {
      'html-basics': true,
      'css-basics': false,
      'javascript-basics': false
    },
    courseAccess: 'approved',
    approvedBy: 'admin',
    approvedAt: new Date().toISOString()
  },
  {
    id: 3,
    username: 'newuser',
    email: '<EMAIL>',
    password: 'newuser123',
    role: 'user',
    progress: {},
    courseAccess: 'pending',
    approvedBy: null,
    approvedAt: null
  }
];

// Authentication middleware
const requireAuth = (req, res, next) => {
  if (req.session.user) {
    next();
  } else {
    res.status(401).json({ error: 'Authentication required' });
  }
};

const requireAdmin = (req, res, next) => {
  if (req.session.user && req.session.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({ error: 'Admin access required' });
  }
};

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Authentication routes
app.post('/api/login', (req, res) => {
  const { username, password } = req.body;
  
  const user = users.find(u => u.username === username && u.password === password);
  
  if (user) {
    req.session.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    };
    res.json({ 
      success: true, 
      user: req.session.user,
      message: 'Login successful' 
    });
  } else {
    res.status(401).json({ 
      success: false, 
      message: 'Invalid credentials' 
    });
  }
});

app.post('/api/register', (req, res) => {
  const { username, email, password } = req.body;
  
  // Check if user already exists
  const existingUser = users.find(u => u.username === username || u.email === email);
  
  if (existingUser) {
    return res.status(400).json({ 
      success: false, 
      message: 'User already exists' 
    });
  }
  
  // Create new user
  const newUser = {
    id: users.length + 1,
    username,
    email,
    password, // In production, hash this password
    role: 'user',
    progress: {},
    courseAccess: 'pending',
    approvedBy: null,
    approvedAt: null
  };
  
  users.push(newUser);
  
  req.session.user = {
    id: newUser.id,
    username: newUser.username,
    email: newUser.email,
    role: newUser.role
  };
  
  res.json({ 
    success: true, 
    user: req.session.user,
    message: 'Registration successful' 
  });
});

app.post('/api/logout', (req, res) => {
  req.session.destroy();
  res.json({ success: true, message: 'Logout successful' });
});

// User routes
app.get('/api/user/profile', requireAuth, (req, res) => {
  const user = users.find(u => u.id === req.session.user.id);
  res.json({ user: user });
});

app.put('/api/user/progress', requireAuth, (req, res) => {
  const { lesson, completed } = req.body;
  const user = users.find(u => u.id === req.session.user.id);
  
  if (user) {
    user.progress[lesson] = completed;
    res.json({ success: true, progress: user.progress });
  } else {
    res.status(404).json({ error: 'User not found' });
  }
});

// Admin routes
app.get('/api/admin/users', requireAdmin, (req, res) => {
  const userList = users.map(u => ({
    id: u.id,
    username: u.username,
    email: u.email,
    role: u.role,
    progress: u.progress
  }));
  res.json({ users: userList });
});

app.delete('/api/admin/users/:id', requireAdmin, (req, res) => {
  const userId = parseInt(req.params.id);
  const userIndex = users.findIndex(u => u.id === userId);

  if (userIndex !== -1) {
    users.splice(userIndex, 1);
    res.json({ success: true, message: 'User deleted successfully' });
  } else {
    res.status(404).json({ error: 'User not found' });
  }
});

// Course access management
app.put('/api/admin/users/:id/approve', requireAdmin, (req, res) => {
  const userId = parseInt(req.params.id);
  const user = users.find(u => u.id === userId);

  if (user) {
    user.courseAccess = 'approved';
    user.approvedBy = req.session.user.username;
    user.approvedAt = new Date().toISOString();
    res.json({ success: true, message: 'User approved for course access' });
  } else {
    res.status(404).json({ error: 'User not found' });
  }
});

app.put('/api/admin/users/:id/reject', requireAdmin, (req, res) => {
  const userId = parseInt(req.params.id);
  const user = users.find(u => u.id === userId);

  if (user) {
    user.courseAccess = 'rejected';
    user.approvedBy = req.session.user.username;
    user.approvedAt = new Date().toISOString();
    res.json({ success: true, message: 'User course access rejected' });
  } else {
    res.status(404).json({ error: 'User not found' });
  }
});

// Check course access
app.get('/api/course/access', requireAuth, (req, res) => {
  const user = users.find(u => u.id === req.session.user.id);

  if (user) {
    res.json({
      hasAccess: user.courseAccess === 'approved',
      status: user.courseAccess,
      approvedBy: user.approvedBy,
      approvedAt: user.approvedAt
    });
  } else {
    res.status(404).json({ error: 'User not found' });
  }
});

// Check authentication status
app.get('/api/auth/status', (req, res) => {
  if (req.session.user) {
    res.json({ authenticated: true, user: req.session.user });
  } else {
    res.json({ authenticated: false });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Zero to Hero Developer website running on http://localhost:${PORT}`);
  console.log('📚 Ready to help developers on their journey!');
});
