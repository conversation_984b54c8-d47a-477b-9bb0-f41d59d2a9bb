<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In - Zero to <PERSON>per</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-code"></i>
                <span>Zero to Hero</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="roadmap.html">Roadmap</a></li>
                <li><a href="#" class="auth-btn active">Sign In</a></li>
            </ul>
        </div>
    </nav>

    <!-- Authentication Section -->
    <section class="auth-section">
        <div class="auth-container">
            <div class="auth-tabs">
                <button class="tab-btn active" data-tab="login">Sign In</button>
                <button class="tab-btn" data-tab="register">Sign Up</button>
                <button class="tab-btn" data-tab="admin">Admin</button>
            </div>

            <!-- Login Form -->
            <div id="login" class="auth-form active">
                <h2>Welcome Back!</h2>
                <p>Sign in to continue your learning journey</p>
                
                <form id="loginForm">
                    <div class="form-group">
                        <label for="loginUsername">Username</label>
                        <input type="text" id="loginUsername" name="username" required>
                        <i class="fas fa-user"></i>
                    </div>
                    
                    <div class="form-group">
                        <label for="loginPassword">Password</label>
                        <input type="password" id="loginPassword" name="password" required>
                        <i class="fas fa-lock"></i>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Sign In</button>
                </form>
                
                <div class="demo-credentials">
                    <h4>Available Accounts:</h4>
                    <p><strong>Admin 1:</strong> username: admin, password: admin123</p>
                    <p><strong>Admin 2:</strong> username: mohan, password: mohan@2005</p>
                    <p><strong>User 1:</strong> username: user1, password: user123</p>
                    <p><strong>User 2:</strong> username: karthi k, password: karthi@2005</p>
                    <p><strong>Pending User:</strong> username: newuser, password: newuser123</p>
                </div>
            </div>

            <!-- Register Form -->
            <div id="register" class="auth-form">
                <h2>Join Our Community!</h2>
                <p>Create an account to start your developer journey</p>
                
                <form id="registerForm">
                    <div class="form-group">
                        <label for="registerUsername">Username</label>
                        <input type="text" id="registerUsername" name="username" required>
                        <i class="fas fa-user"></i>
                    </div>
                    
                    <div class="form-group">
                        <label for="registerEmail">Email</label>
                        <input type="email" id="registerEmail" name="email" required>
                        <i class="fas fa-envelope"></i>
                    </div>
                    
                    <div class="form-group">
                        <label for="registerPassword">Password</label>
                        <input type="password" id="registerPassword" name="password" required>
                        <i class="fas fa-lock"></i>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirmPassword">Confirm Password</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" required>
                        <i class="fas fa-lock"></i>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Sign Up</button>
                </form>
            </div>

            <!-- Admin Login Form -->
            <div id="admin" class="auth-form">
                <h2>Admin Access</h2>
                <p>Administrative portal for managing users and content</p>
                
                <form id="adminForm">
                    <div class="form-group">
                        <label for="adminUsername">Admin Username</label>
                        <input type="text" id="adminUsername" name="username" required>
                        <i class="fas fa-user-shield"></i>
                    </div>
                    
                    <div class="form-group">
                        <label for="adminPassword">Admin Password</label>
                        <input type="password" id="adminPassword" name="password" required>
                        <i class="fas fa-key"></i>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Admin Sign In</button>
                </form>
                
                <div class="admin-note">
                    <i class="fas fa-info-circle"></i>
                    <p>Admin access is restricted to authorized personnel only.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Success/Error Messages -->
    <div id="messageContainer" class="message-container"></div>

    <script src="js/auth.js"></script>
</body>
</html>
