// Authentication JavaScript for Zero to Hero Developer Website

document.addEventListener('DOMContentLoaded', function() {
    // Tab Switching
    const tabButtons = document.querySelectorAll('.tab-btn');
    const authForms = document.querySelectorAll('.auth-form');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // Remove active class from all tabs and forms
            tabButtons.forEach(btn => btn.classList.remove('active'));
            authForms.forEach(form => form.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding form
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });

    // Form Submissions
    setupFormHandlers();
    
    // Check if user is already authenticated
    checkAuthenticationStatus();
});

function setupFormHandlers() {
    // Login Form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    // Register Form
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }

    // Admin Form
    const adminForm = document.getElementById('adminForm');
    if (adminForm) {
        adminForm.addEventListener('submit', handleAdminLogin);
    }
}

async function handleLogin(e) {
    e.preventDefault();
    
    const form = e.target;
    const submitButton = form.querySelector('button[type="submit"]');
    const formData = new FormData(form);
    
    const loginData = {
        username: formData.get('username'),
        password: formData.get('password')
    };

    // Validation
    if (!loginData.username || !loginData.password) {
        showMessage('Please fill in all fields', 'error');
        return;
    }

    setLoadingState(submitButton, true);

    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(loginData)
        });

        const data = await response.json();

        if (data.success) {
            showMessage('Login successful! Redirecting...', 'success');
            
            // Save user data to localStorage
            localStorage.setItem('user', JSON.stringify(data.user));
            
            // Redirect based on user role
            setTimeout(() => {
                if (data.user.role === 'admin') {
                    window.location.href = 'admin.html';
                } else {
                    window.location.href = 'dashboard.html';
                }
            }, 1500);
        } else {
            showMessage(data.message || 'Login failed', 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showMessage('An error occurred during login', 'error');
    } finally {
        setLoadingState(submitButton, false);
    }
}

async function handleRegister(e) {
    e.preventDefault();
    
    const form = e.target;
    const submitButton = form.querySelector('button[type="submit"]');
    const formData = new FormData(form);
    
    const registerData = {
        username: formData.get('username'),
        email: formData.get('email'),
        password: formData.get('password'),
        confirmPassword: formData.get('confirmPassword')
    };

    // Validation
    if (!registerData.username || !registerData.email || !registerData.password || !registerData.confirmPassword) {
        showMessage('Please fill in all fields', 'error');
        return;
    }

    if (!validateEmail(registerData.email)) {
        showMessage('Please enter a valid email address', 'error');
        return;
    }

    if (registerData.password !== registerData.confirmPassword) {
        showMessage('Passwords do not match', 'error');
        return;
    }

    if (!validatePassword(registerData.password)) {
        showMessage('Password must be at least 6 characters long', 'error');
        return;
    }

    setLoadingState(submitButton, true);

    try {
        const response = await fetch('/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: registerData.username,
                email: registerData.email,
                password: registerData.password
            })
        });

        const data = await response.json();

        if (data.success) {
            showMessage('Registration successful! Redirecting to dashboard...', 'success');
            
            // Save user data to localStorage
            localStorage.setItem('user', JSON.stringify(data.user));
            
            // Redirect to dashboard
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 1500);
        } else {
            showMessage(data.message || 'Registration failed', 'error');
        }
    } catch (error) {
        console.error('Registration error:', error);
        showMessage('An error occurred during registration', 'error');
    } finally {
        setLoadingState(submitButton, false);
    }
}

async function handleAdminLogin(e) {
    e.preventDefault();

    const form = e.target;
    const submitButton = form.querySelector('button[type="submit"]');
    const formData = new FormData(form);

    const adminData = {
        username: formData.get('username'),
        password: formData.get('password')
    };

    // Validation
    if (!adminData.username || !adminData.password) {
        showMessage('Please fill in all fields', 'error');
        return;
    }

    setLoadingState(submitButton, true);

    try {
        console.log('Attempting admin login with:', adminData);

        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(adminData)
        });

        console.log('Response status:', response.status);
        const data = await response.json();
        console.log('Response data:', data);

        if (data.success && data.user.role === 'admin') {
            showMessage('Admin login successful! Redirecting...', 'success');

            // Save user data to localStorage
            localStorage.setItem('user', JSON.stringify(data.user));

            // Redirect to admin panel
            setTimeout(() => {
                window.location.href = 'admin.html';
            }, 1500);
        } else if (data.success && data.user.role !== 'admin') {
            showMessage('Access denied. Admin privileges required.', 'error');
            // Also logout the user since they're not admin
            await fetch('/api/logout', { method: 'POST' });
            localStorage.removeItem('user');
        } else {
            showMessage(data.message || 'Admin login failed', 'error');
        }
    } catch (error) {
        console.error('Admin login error:', error);
        showMessage('Connection error. Please check if the server is running.', 'error');
    } finally {
        setLoadingState(submitButton, false);
    }
}

async function checkAuthenticationStatus() {
    try {
        const response = await fetch('/api/auth/status');
        const data = await response.json();
        
        if (data.authenticated) {
            // User is already logged in, redirect to appropriate page
            if (data.user.role === 'admin') {
                window.location.href = 'admin.html';
            } else {
                window.location.href = 'dashboard.html';
            }
        }
    } catch (error) {
        // User is not authenticated, stay on auth page
        console.log('User not authenticated');
    }
}

// Utility Functions
function showMessage(message, type = 'info') {
    const messageContainer = document.getElementById('messageContainer') || createMessageContainer();
    
    const messageElement = document.createElement('div');
    messageElement.className = `message ${type}`;
    messageElement.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()" style="background: none; border: none; color: white; float: right; cursor: pointer;">&times;</button>
    `;
    
    messageContainer.appendChild(messageElement);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (messageElement.parentElement) {
            messageElement.remove();
        }
    }, 5000);
}

function createMessageContainer() {
    const container = document.createElement('div');
    container.id = 'messageContainer';
    container.className = 'message-container';
    document.body.appendChild(container);
    return container;
}

function setLoadingState(button, isLoading) {
    if (isLoading) {
        button.disabled = true;
        button.classList.add('loading');
        button.dataset.originalText = button.textContent;
        button.textContent = 'Loading...';
    } else {
        button.disabled = false;
        button.classList.remove('loading');
        button.textContent = button.dataset.originalText || button.textContent;
    }
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePassword(password) {
    return password.length >= 6;
}
