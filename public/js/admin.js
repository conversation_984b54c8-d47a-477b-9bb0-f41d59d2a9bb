// Admin Panel JavaScript for Zero to Hero Developer Website

document.addEventListener('DOMContentLoaded', function() {
    // Check admin authentication
    checkAdminAuthentication();
    
    // Setup event listeners
    setupEventListeners();
    
    // Load initial data
    loadDashboardData();
    
    // Load users
    loadUsers();
});

function checkAdminAuthentication() {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    
    if (!user.username || user.role !== 'admin') {
        // User not authenticated as admin, redirect to auth page
        window.location.href = 'auth.html';
        return;
    }
}

function setupEventListeners() {
    // Tab switching
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // Remove active class from all tabs and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });

    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }

    // Refresh users button
    const refreshUsersBtn = document.getElementById('refreshUsers');
    if (refreshUsersBtn) {
        refreshUsersBtn.addEventListener('click', loadUsers);
    }

    // Export data button
    const exportDataBtn = document.getElementById('exportData');
    if (exportDataBtn) {
        exportDataBtn.addEventListener('click', exportData);
    }

    // Add content button
    const addContentBtn = document.getElementById('addContent');
    if (addContentBtn) {
        addContentBtn.addEventListener('click', function() {
            showMessage('Add content functionality would be implemented here', 'info');
        });
    }

    // Modal event listeners
    setupModalListeners();
}

function setupModalListeners() {
    const modal = document.getElementById('confirmModal');
    const cancelBtn = document.getElementById('cancelAction');
    const confirmBtn = document.getElementById('confirmAction');

    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            modal.classList.remove('active');
        });
    }

    // Close modal when clicking outside
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.classList.remove('active');
            }
        });
    }
}

async function handleLogout() {
    try {
        const response = await fetch('/api/logout', {
            method: 'POST'
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Clear local storage
            localStorage.removeItem('user');
            
            // Show success message
            showMessage('Logged out successfully', 'success');
            
            // Redirect to home page
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);
        }
    } catch (error) {
        console.error('Logout error:', error);
        // Clear local storage anyway
        localStorage.removeItem('user');
        window.location.href = 'index.html';
    }
}

function loadDashboardData() {
    // Update stats with mock data
    document.getElementById('totalUsers').textContent = '1,234';
    document.getElementById('activeUsers').textContent = '892';
    document.getElementById('totalLessons').textContent = '12';
    document.getElementById('completionRate').textContent = '85%';
}

async function loadUsers() {
    const refreshBtn = document.getElementById('refreshUsers');
    if (refreshBtn) setLoadingState(refreshBtn, true);

    try {
        console.log('Loading users...');
        const response = await fetch('/api/admin/users');
        console.log('Users response status:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Users data:', data);

        if (data.users) {
            displayUsers(data.users);
            showMessage('Users loaded successfully', 'success');
        } else {
            throw new Error('No users data received');
        }
    } catch (error) {
        console.error('Error loading users:', error);
        showMessage('Error connecting to server. Using demo data.', 'error');

        // Show mock data for demo
        const mockUsers = [
            {
                id: 1,
                username: 'admin',
                email: '<EMAIL>',
                role: 'admin',
                courseAccess: 'approved',
                progress: { 'html-basics': true, 'css-basics': true, 'javascript-basics': true }
            },
            {
                id: 2,
                username: 'user1',
                email: '<EMAIL>',
                role: 'user',
                courseAccess: 'approved',
                progress: { 'html-basics': true, 'css-basics': false, 'javascript-basics': false }
            },
            {
                id: 3,
                username: 'newuser',
                email: '<EMAIL>',
                role: 'user',
                courseAccess: 'pending',
                progress: {}
            }
        ];
        displayUsers(mockUsers);
    } finally {
        if (refreshBtn) setLoadingState(refreshBtn, false);
    }
}

function displayUsers(users) {
    const tbody = document.getElementById('usersTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    users.forEach(user => {
        const progressCount = Object.values(user.progress || {}).filter(Boolean).length;
        const totalLessons = 4; // Total number of lessons
        const progressPercentage = Math.round((progressCount / totalLessons) * 100);

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${user.id}</td>
            <td>${user.username}</td>
            <td>${user.email}</td>
            <td><span class="user-role ${user.role}">${user.role}</span></td>
            <td><span class="course-access ${user.courseAccess || 'pending'}">${user.courseAccess || 'pending'}</span></td>
            <td>
                <div class="progress-indicator">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progressPercentage}%"></div>
                    </div>
                    <span>${progressPercentage}%</span>
                </div>
            </td>
            <td>
                <div class="action-buttons">
                    ${user.role !== 'admin' && user.courseAccess !== 'approved' ? `
                        <button class="btn btn-primary btn-small" onclick="approveCourseAccess(${user.id}, '${user.username}')">
                            <i class="fas fa-check"></i> Approve
                        </button>
                    ` : ''}
                    ${user.role !== 'admin' && user.courseAccess !== 'rejected' ? `
                        <button class="btn btn-danger btn-small" onclick="rejectCourseAccess(${user.id}, '${user.username}')">
                            <i class="fas fa-times"></i> Reject
                        </button>
                    ` : ''}
                    <button class="btn btn-secondary btn-small" onclick="editUser(${user.id})">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    ${user.role !== 'admin' ? `
                        <button class="btn btn-danger btn-small" onclick="deleteUser(${user.id}, '${user.username}')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    ` : ''}
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function editUser(userId) {
    showMessage(`Edit user functionality would be implemented for user ID: ${userId}`, 'info');
}

async function approveCourseAccess(userId, username) {
    try {
        const response = await fetch(`/api/admin/users/${userId}/approve`, {
            method: 'PUT'
        });

        const data = await response.json();

        if (data.success) {
            showMessage(`Course access approved for "${username}"`, 'success');
            loadUsers(); // Reload users list
        } else {
            showMessage(data.error || 'Failed to approve course access', 'error');
        }
    } catch (error) {
        console.error('Error approving course access:', error);
        showMessage('Error approving course access', 'error');
    }
}

async function rejectCourseAccess(userId, username) {
    const modal = document.getElementById('confirmModal');
    const confirmMessage = document.getElementById('confirmMessage');
    const confirmBtn = document.getElementById('confirmAction');

    confirmMessage.textContent = `Are you sure you want to reject course access for "${username}"?`;

    // Remove existing event listeners
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

    // Add new event listener
    newConfirmBtn.addEventListener('click', async function() {
        modal.classList.remove('active');
        await performRejectCourseAccess(userId, username);
    });

    modal.classList.add('active');
}

async function performRejectCourseAccess(userId, username) {
    try {
        const response = await fetch(`/api/admin/users/${userId}/reject`, {
            method: 'PUT'
        });

        const data = await response.json();

        if (data.success) {
            showMessage(`Course access rejected for "${username}"`, 'success');
            loadUsers(); // Reload users list
        } else {
            showMessage(data.error || 'Failed to reject course access', 'error');
        }
    } catch (error) {
        console.error('Error rejecting course access:', error);
        showMessage('Error rejecting course access', 'error');
    }
}

function deleteUser(userId, username) {
    const modal = document.getElementById('confirmModal');
    const confirmMessage = document.getElementById('confirmMessage');
    const confirmBtn = document.getElementById('confirmAction');

    confirmMessage.textContent = `Are you sure you want to delete user "${username}"? This action cannot be undone.`;
    
    // Remove existing event listeners
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    
    // Add new event listener
    newConfirmBtn.addEventListener('click', async function() {
        modal.classList.remove('active');
        await performDeleteUser(userId, username);
    });

    modal.classList.add('active');
}

async function performDeleteUser(userId, username) {
    try {
        const response = await fetch(`/api/admin/users/${userId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            showMessage(`User "${username}" deleted successfully`, 'success');
            loadUsers(); // Reload users list
        } else {
            showMessage(data.error || 'Failed to delete user', 'error');
        }
    } catch (error) {
        console.error('Error deleting user:', error);
        showMessage('Error deleting user', 'error');
    }
}

function exportData() {
    // Mock export functionality
    showMessage('Exporting data...', 'info');
    
    setTimeout(() => {
        const data = {
            users: 1234,
            lessons: 12,
            completions: 8945,
            exportDate: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'zero-to-hero-data.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showMessage('Data exported successfully', 'success');
    }, 1000);
}

// Utility Functions
function setLoadingState(button, isLoading) {
    if (isLoading) {
        button.disabled = true;
        button.classList.add('loading');
        button.dataset.originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    } else {
        button.disabled = false;
        button.classList.remove('loading');
        button.innerHTML = button.dataset.originalText || button.innerHTML;
    }
}

function showMessage(message, type = 'info') {
    const messageContainer = document.getElementById('messageContainer') || createMessageContainer();
    
    const messageElement = document.createElement('div');
    messageElement.className = `message ${type}`;
    messageElement.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()" style="background: none; border: none; color: white; float: right; cursor: pointer;">&times;</button>
    `;
    
    messageContainer.appendChild(messageElement);
    
    setTimeout(() => {
        if (messageElement.parentElement) {
            messageElement.remove();
        }
    }, 5000);
}

function createMessageContainer() {
    const container = document.createElement('div');
    container.id = 'messageContainer';
    container.className = 'message-container';
    document.body.appendChild(container);
    return container;
}
