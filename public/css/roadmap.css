/* Roadmap Specific Styles */
.roadmap-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
}

.roadmap-header h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.roadmap-header p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.roadmap-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.roadmap-stats .stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255,255,255,0.1);
    padding: 1rem 1.5rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.roadmap-stats .stat i {
    font-size: 1.2rem;
}

.roadmap-content {
    padding: 80px 0;
    background: #f8f9fa;
}

/* Phase Sections */
.phase-section {
    margin-bottom: 4rem;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.phase-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 2rem;
}

.phase-number {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    backdrop-filter: blur(10px);
}

.phase-info h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.phase-info p {
    opacity: 0.9;
    margin-bottom: 1rem;
}

.phase-duration {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Lessons Grid */
.lessons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    padding: 2rem;
}

.lesson-card {
    background: #f8f9fa;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
}

.lesson-card:hover {
    border-color: #667eea;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.1);
}

.lesson-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.lesson-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.lesson-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.lesson-topics {
    list-style: none;
    padding: 0;
    margin-bottom: 1.5rem;
}

.lesson-topics li {
    padding: 0.5rem 0;
    color: #555;
    position: relative;
    padding-left: 1.5rem;
}

.lesson-topics li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

.lesson-difficulty {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.difficulty {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty.beginner {
    background: #28a745;
    color: white;
}

.difficulty.intermediate {
    background: #ffc107;
    color: #333;
}

.difficulty.advanced {
    background: #dc3545;
    color: white;
}

/* Call to Action */
.cta-section {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 15px;
    margin-top: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.cta-section h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.cta-section p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-section .btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Progress Indicators */
.phase-progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255,255,255,0.3);
    overflow: hidden;
}

.phase-progress-fill {
    height: 100%;
    background: #ffd700;
    transition: width 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .roadmap-header h1 {
        font-size: 2.5rem;
    }
    
    .roadmap-stats {
        gap: 1rem;
    }
    
    .roadmap-stats .stat {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .phase-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .phase-number {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .phase-info h2 {
        font-size: 1.5rem;
    }
    
    .lessons-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }
    
    .lesson-card {
        padding: 1.5rem;
    }
    
    .lesson-difficulty {
        position: static;
        margin-top: 1rem;
    }
    
    .cta-section {
        padding: 2rem 1rem;
    }
    
    .cta-section h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .roadmap-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .lessons-grid {
        gap: 1rem;
    }
    
    .lesson-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}
