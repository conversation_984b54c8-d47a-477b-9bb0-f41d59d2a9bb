/* Dashboard Styles */
.dashboard {
    padding-top: 100px;
    min-height: 100vh;
    background: #f8f9fa;
}

/* Welcome Section */
.welcome-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 0;
    margin-bottom: 40px;
    border-radius: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.welcome-content h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.welcome-content p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.user-stats {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.stat-card {
    background: rgba(255,255,255,0.1);
    padding: 1.5rem;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 150px;
    backdrop-filter: blur(10px);
}

.stat-card i {
    font-size: 2rem;
    color: #ffd700;
}

.stat-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
}

.stat-card p {
    font-size: 0.9rem;
    opacity: 0.8;
    margin: 0;
}

/* Progress Section */
.progress-section {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.progress-section h2 {
    margin-bottom: 2rem;
    color: #333;
}

.progress-overview {
    display: flex;
    align-items: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.progress-circle {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    transition: stroke-dasharray 0.35s;
    transform-origin: 50% 50%;
}

.progress-text {
    position: absolute;
    text-align: center;
}

.progress-text span {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
}

.progress-text small {
    display: block;
    color: #666;
    font-size: 0.8rem;
}

.progress-details h3 {
    color: #333;
    margin-bottom: 1rem;
}

.progress-details p {
    color: #666;
    margin-bottom: 1rem;
}

.next-milestone {
    padding: 1rem;
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.course-access-status {
    padding: 1rem;
    border-radius: 5px;
    margin-top: 1rem;
}

.course-access-status.approved {
    background: #d4edda;
    border-left: 4px solid #28a745;
    color: #155724;
}

.course-access-status.pending {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    color: #856404;
}

.course-access-status.rejected {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.course-access-status strong {
    display: block;
    margin-bottom: 0.5rem;
}

.course-access-status .btn {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Learning Path */
.learning-path {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.learning-path h2 {
    margin-bottom: 2rem;
    color: #333;
}

.path-container {
    display: grid;
    gap: 1.5rem;
}

.lesson-card {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.lesson-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
}

.lesson-card.completed {
    border-color: #28a745;
    background: #f8fff9;
}

.lesson-card.in-progress {
    border-color: #ffc107;
    background: #fffdf5;
}

.lesson-card.locked {
    opacity: 0.6;
    cursor: not-allowed;
}

.lesson-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    font-size: 1.5rem;
}

.lesson-card.completed .lesson-icon {
    background: #28a745;
    color: white;
}

.lesson-card.in-progress .lesson-icon {
    background: #ffc107;
    color: white;
}

.lesson-card.locked .lesson-icon {
    background: #e1e5e9;
    color: #999;
}

.lesson-content {
    flex: 1;
}

.lesson-content h3 {
    margin-bottom: 0.5rem;
    color: #333;
}

.lesson-content p {
    color: #666;
    margin-bottom: 1rem;
}

.lesson-progress {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #e1e5e9;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.lesson-status {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.lesson-status.completed {
    background: #28a745;
}

.lesson-status.in-progress {
    background: #ffc107;
}

.lesson-status.locked {
    background: #e1e5e9;
    color: #999;
}

/* Recent Activity */
.recent-activity {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.recent-activity h2 {
    margin-bottom: 2rem;
    color: #333;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    background: #f8f9fa;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.activity-icon.completed {
    background: #28a745;
}

.activity-icon.in-progress {
    background: #ffc107;
}

.activity-icon.achievement {
    background: #ff6b35;
}

.activity-content h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.activity-content p {
    color: #666;
    margin-bottom: 0.5rem;
}

.activity-content small {
    color: #999;
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-section {
        flex-direction: column;
        text-align: center;
    }
    
    .user-stats {
        justify-content: center;
    }
    
    .progress-overview {
        flex-direction: column;
        text-align: center;
    }
    
    .lesson-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .lesson-icon {
        margin-right: 0;
    }
    
    .activity-item {
        flex-direction: column;
        text-align: center;
    }
}
