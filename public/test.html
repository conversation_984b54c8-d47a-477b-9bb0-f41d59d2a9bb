<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connection Test - Zero to <PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a6fd8;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Backend Connection Test</h1>
        <p>This page tests the connection between frontend and backend services.</p>
        
        <div class="test-section">
            <h3>Server Connection Test</h3>
            <button onclick="testServerConnection()">Test Server</button>
            <div id="serverResult"></div>
        </div>
        
        <div class="test-section">
            <h3>Authentication Test</h3>
            <button onclick="testLogin()">Test Admin Login</button>
            <button onclick="testUserLogin()">Test User Login</button>
            <div id="authResult"></div>
        </div>
        
        <div class="test-section">
            <h3>API Endpoints Test</h3>
            <button onclick="testAllEndpoints()">Test All APIs</button>
            <div id="apiResult"></div>
        </div>
        
        <div class="test-section">
            <h3>Session Test</h3>
            <button onclick="testSession()">Check Session</button>
            <div id="sessionResult"></div>
        </div>
        
        <div class="test-section">
            <a href="index.html" style="color: #667eea; text-decoration: none;">← Back to Main Site</a>
        </div>
    </div>

    <script>
        async function testServerConnection() {
            const resultDiv = document.getElementById('serverResult');
            resultDiv.innerHTML = '<div class="loading">Testing server connection...</div>';
            
            try {
                const response = await fetch('/api/test');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Server is running!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Server responded but with error</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Cannot connect to server</div>
                    <pre>Error: ${error.message}</pre>
                    <p>Make sure the server is running on http://localhost:3000</p>
                `;
            }
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('authResult');
            resultDiv.innerHTML = '<div class="loading">Testing admin login...</div>';
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.user.role === 'admin') {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Admin login successful!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Admin login failed</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Login request failed</div>
                    <pre>Error: ${error.message}</pre>
                `;
            }
        }
        
        async function testUserLogin() {
            const resultDiv = document.getElementById('authResult');
            resultDiv.innerHTML = '<div class="loading">Testing user login...</div>';
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'user1',
                        password: 'user123'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ User login successful!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ User login failed</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Login request failed</div>
                    <pre>Error: ${error.message}</pre>
                `;
            }
        }
        
        async function testAllEndpoints() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div class="loading">Testing all API endpoints...</div>';
            
            const endpoints = [
                { method: 'GET', url: '/api/test', name: 'Test Endpoint' },
                { method: 'GET', url: '/api/auth/status', name: 'Auth Status' },
            ];
            
            let results = [];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url, { method: endpoint.method });
                    const data = await response.json();
                    results.push({
                        name: endpoint.name,
                        status: response.status,
                        success: response.ok,
                        data: data
                    });
                } catch (error) {
                    results.push({
                        name: endpoint.name,
                        status: 'Error',
                        success: false,
                        error: error.message
                    });
                }
            }
            
            let html = '';
            results.forEach(result => {
                const className = result.success ? 'success' : 'error';
                const icon = result.success ? '✅' : '❌';
                html += `
                    <div class="${className}">
                        ${icon} ${result.name}: ${result.status}
                    </div>
                `;
            });
            
            resultDiv.innerHTML = html + `<pre>${JSON.stringify(results, null, 2)}</pre>`;
        }
        
        async function testSession() {
            const resultDiv = document.getElementById('sessionResult');
            resultDiv.innerHTML = '<div class="loading">Checking session...</div>';
            
            try {
                const response = await fetch('/api/auth/status');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Session check complete</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Session check failed</div>
                    <pre>Error: ${error.message}</pre>
                `;
            }
        }
        
        // Auto-test server connection on page load
        window.addEventListener('load', () => {
            testServerConnection();
        });
    </script>
</body>
</html>
